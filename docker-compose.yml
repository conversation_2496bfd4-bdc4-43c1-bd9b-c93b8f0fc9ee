version: '3.8'

services:
  recaptcha-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: recaptcha-api
    ports:
      - "8042:8042"
    environment:
      # 可选的代理设置，如果需要使用代理请取消注释并修改
      # - proxy=http://127.0.0.1:7890
      - PYTHONUNBUFFERED=1
      - DISPLAY=:99
      - CHROME_BIN=/app/chrome/chrome-linux64/chrome
      - CHROMEDRIVER_PATH=/app/driver/chromedriver
    volumes:
      - ./screenshots:/app/screenshots
      - ./temp_chrome_profile:/app/temp_chrome_profile
      - ./driver:/app/driver
      - ./fingerprint-chromium:/app/fingerprint-chromium

    networks:
      - recaptcha-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8042/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  recaptcha-network:
    driver: bridge

volumes:
  screenshots:
    driver: local
  chrome_profile:
    driver: local
