# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 更新包列表并安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    # ChromeDriver 运行所需的基础库
    libglib2.0-0 \
    libnss3 \
    libgconf-2-4 \
    libxss1 \
    libappindicator3-1 \
    libindicator7 \
    gconf-service \
    libgconf-2-4 \
    libgconf2-dev \
    libgtk-3-0 \
    libxss1 \
    libgconf-2-4 \
    # X11 相关库
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    # 音频相关库
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    # 其他必要库
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    # 网络和工具
    curl \
    wget \
    unzip \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/screenshots \
    && mkdir -p /app/temp_chrome_profile \
    && mkdir -p /app/driver

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动应用\n\
exec python main.py' > /app/start.sh \
    && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8042

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8042/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
