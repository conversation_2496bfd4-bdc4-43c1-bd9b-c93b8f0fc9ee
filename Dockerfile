# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    gnupg \
    unzip \
    xvfb \
    libxss1 \
    libgconf-2-4 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxi6 \
    libxtst6 \
    libnss3 \
    libcups2 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxss1 \
    lsb-release \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/screenshots \
    && mkdir -p /app/temp_chrome_profile \
    && mkdir -p /app/driver

# 设置chromedriver权限
RUN chmod +x /app/driver/chromedriver

# 设置Chrome二进制文件权限
RUN chmod +x /app/chrome/chrome-linux64/chrome

# 创建非root用户来运行Chrome
RUN groupadd -r chrome && useradd -r -g chrome -G audio,video chrome \
    && mkdir -p /home/<USER>/Downloads \
    && chown -R chrome:chrome /home/<USER>
    && chown -R chrome:chrome /app

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动虚拟显示服务器\n\
Xvfb :99 -screen 0 1920x1080x24 &\n\
export DISPLAY=:99\n\
# 等待Xvfb启动\n\
sleep 2\n\
# 启动应用\n\
exec python main.py' > /app/start.sh \
    && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8042

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8042/health || exit 1

# 切换到非root用户
USER chrome

# 启动应用
CMD ["/app/start.sh"]
